@charset "utf-8";
/* CSS Document */
*{
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box}
*,body{padding:0px;	margin:0px;font-family: "微软雅黑";}
body{ background:#000d4a url(../images/bg.jpg) center center; background-size:cover;color:#fff; font-size: .1rem; }
li{ list-style-type:none;}
@font-face{font-family:electronicFont;src:url(../font/DS-DIGIT.TTF)}
i{ margin:0px; padding:0px; text-indent:0px;}
img{ border:none; max-width: 100%;}
a{ text-decoration:none; color:#399bff;}
a.active,a:focus{ outline:none!important; text-decoration:none;}
ol,ul,p,h1,h2,h3,h4,h5,h6{ padding:0; margin:0}
a:hover{ color:#06c; text-decoration: none!important}

html,body{height: 100%;}
.clearfix:after, .clearfix:before {display: table;content: " "}
 .clearfix:after {clear: both}
.pulll_left{float:left;}
.pulll_right{float:right;}
/*谷哥滚动条样式*/
  ::-webkit-scrollbar {width:5px;height:5px;position:absolute}
  ::-webkit-scrollbar-thumb {background-color:#5bc0de}
  ::-webkit-scrollbar-track {background-color:#ddd}
/***/

.loading{position:fixed; left:0; top:0; font-size:16px; z-index:100000000;width:100%; height:100%; background:#1a1a1c; text-align:center;}
.loadbox{position:absolute; width:160px;height:150px; color: #324e93; left:50%; top:50%; margin-top:-100px; margin-left:-75px;}
.loadbox img{ margin:10px auto; display:block; width:40px;}

.head{ height:1.05rem; background: url(../images/head_bg.png) no-repeat center center; background-size: 100% 100%; position: relative}
.head h1{ color:#fff; text-align: center; font-size: .4rem; line-height:.8rem; letter-spacing: -1px;}
.head h1 img{ width:1.5rem; display: inline-block; vertical-align: middle; }
.time{ position:absolute; right:.15rem; top:0; line-height: .75rem;color:rgba(255,255,255,.7); font-size: .3rem; padding-right: .1rem;font-family:electronicFont;}

.mainbox{ padding:0 .2rem 0rem .2rem; height:calc(100% - 1.05rem) }
.mainbox>ul{ margin-left:-.1rem; margin-right:-.1rem; height:100%}
.mainbox>ul>li{ float: left; padding: 0 .1rem;height:100%;width: 30%}
.mainbox>ul>li:nth-child(2){ width: 40%}

.boxall{ padding:0 .2rem .2rem .2rem;  background: rgba(6,48,109,.5); position: relative; margin-bottom: .15rem; z-index: 10; }
.alltitle{ font-size:.2rem; color:#fff;  line-height: .5rem; position: relative;padding-left: .15rem}
.alltitle:before{position: absolute; height: .2rem; width: 4px; background: #49bcf7; border-radius: 5px; content: ""; left: 0;  top: 50%; margin-top: -.1rem;}
.boxnav{height: calc(100% - .5rem);}
.row>li{ float: left; height: 100%;}
.col-6{width: 50%;}
.col-3{width: 25%;}
.col-4{width: 33.33333%;}
.h100{height: 100%;}
.tit01{ text-align: center; color: white; font-size: .16rem; padding: .3rem 0 .02rem 0;}
.piebox{ height: calc(100% - .5rem); position: relative;}
.piebox:before{ width:.6rem; height:.6rem; content: ""; border: 1px solid #49bcf7; border-radius: 1rem; position: absolute;
left:50%; top: 50%; margin-left: -.31rem; margin-top: -.31rem; opacity: .7;}

.sqzs{margin-right: .2rem;}
.sqzs p{ padding: .2rem 0  .1rem 0;  font-size: .22rem;}
.sqzs h1{height: calc(100% - .65rem); border-bottom: 1px solid rgba(255,255,255,.1); border-top: 1px solid rgba(255,255,255,.1); display: flex; align-items: center; color: #fef000;font-weight: normal; letter-spacing: 2px; font-size: .5rem; justify-content: center;padding-bottom: .05rem;}
.sqzs h1 span{ font-size: .8rem; font-family: Impact, Haettenschweiler, 'Arial Narrow Bold', sans-serif; }

table{ width: 100%; text-align: center;}
table th{ font-size: .16rem; background: rgba(0,0,0,.1);}
table td{ font-size: .16rem; color: rgba(255,255,255,.6);}
table th,table td{ border-bottom:1px solid rgba(255,255,255,.1); padding: .1rem 0;}