<template>
  <div class="bigdata-container">
    <!-- 加载动画 -->
    <div class="loading" v-if="loading">
      <div class="loadbox">
        <img src="@/assets/images/loading.gif" /> 页面加载中...
      </div>
    </div>

    <!-- 头部 -->
    <div class="head">
      <el-button
        type="text"
        class="back-btn"
        @click="goBack"
        icon="el-icon-arrow-left"
      >
        返回数据看板
      </el-button>
      <h1>大数据可视化系统数据分析平台</h1>
      <div class="time" id="showTime">
        {{ currentTime }}
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="mainbox">
      <ul class="clearfix">
        <!-- 左侧列 -->
        <li>
          <div class="boxall" style="height: calc(58% - .15rem)">
            <div class="alltitle">
              近七日订单趋势
            </div>
            <div class="boxnav" id="echarts4">
              <commonChart 
                v-if="chartData1.length > 0" 
                :title="chart1.title" 
                :color="chart1.color" 
                :chart-type="chart1.chartType" 
                :head-list="chart1.header" 
                :data-list="chartData1" 
                width="100%" 
                id="chart4" 
                height="100%"
              />
            </div>
          </div>
          <div class="boxall" style="height: calc(42% - .15rem)">
            <div class="alltitle">
              会员活跃度分析
            </div>
            <div class="boxnav" id="echarts3">
              <commonChart 
                v-if="chartData2.length > 0" 
                :title="chart2.title" 
                :color="chart2.color" 
                :chart-type="chart2.chartType" 
                :head-list="chart2.header" 
                :data-list="chartData2" 
                width="100%" 
                id="chart3" 
                height="100%"
              />
            </div>
          </div>
        </li>

        <!-- 中间列 -->
        <li>
          <div class="boxall" style="height: calc(20% - .15rem)">
            <ul class="row h100 clearfix">
              <li class="col-6">
                <div class="sqzs h100">
                  <p>业绩总览</p>
                  <h1><span>{{ formatNumber(homeData.totalPay) }}</span>万</h1>
                </div>
              </li>
              <li class="col-6">
                <ul class="row h100 clearfix">
                  <li class="col-4">
                    <div class="tit01">
                      今日订单
                    </div>
                    <div class="piebox" id="pe01">
                      <div class="pie-number">{{ homeData.todayOrder }}</div>
                    </div>
                  </li>
                  <li class="col-4">
                    <div class="tit01">
                      今日会员
                    </div>
                    <div class="piebox" id="pe02">
                      <div class="pie-number">{{ homeData.todayUser }}</div>
                    </div>
                  </li>
                  <li class="col-4">
                    <div class="tit01">
                      活跃用户
                    </div>
                    <div class="piebox" id="pe03">
                      <div class="pie-number">{{ homeData.todayActiveUser }}</div>
                    </div>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
          <div class="boxall" style="height: calc(38% - .15rem)">
            <div class="alltitle">
              实时数据统计
            </div>
            <div class="boxnav" id="echarts1">
              <gradientChart 
                v-if="chartData1.length > 0" 
                :title="chart1.title" 
                :data-list="chartData1" 
                width="100%" 
                id="chart1" 
                height="100%"
              />
            </div>
          </div>
          <div class="boxall" style="height: calc(42% - .15rem)">
            <div class="alltitle">
              交易金额趋势
            </div>
            <div class="boxnav" id="echarts2">
              <commonChart 
                v-if="paymentData.length > 0" 
                :title="paymentChart.title" 
                :color="paymentChart.color" 
                :chart-type="paymentChart.chartType" 
                :head-list="paymentChart.header" 
                :data-list="paymentData" 
                width="100%" 
                id="chart2" 
                height="100%"
              />
            </div>
          </div>
        </li>

        <!-- 右侧列 -->
        <li>
          <div class="boxall" style="height: calc(33.333% - .15rem)">
            <div class="alltitle">
              数据分布图
            </div>
            <div class="boxnav" id="echarts5">
              <commonChart 
                v-if="distributionData.length > 0" 
                :title="distributionChart.title" 
                :color="distributionChart.color" 
                :chart-type="distributionChart.chartType" 
                :head-list="distributionChart.header" 
                :data-list="distributionData" 
                width="100%" 
                id="chart5" 
                height="100%"
              />
            </div>
          </div>
          <div class="boxall" style="height: calc(33.333% - .15rem)">
            <div class="alltitle">
              核心指标
            </div>
            <div class="boxnav">
              <table border="0" cellspacing="0">
                <tbody>
                <tr>
                  <th></th>
                  <th>今日</th>
                  <th>昨日</th>
                  <th>本月</th>
                  <th>总计</th>
                </tr>
                <tr>
                  <th>订单数</th>
                  <td>{{ homeData.todayOrder }}</td>
                  <td>{{ yesterdayData.order || 0 }}</td>
                  <td>{{ monthData.order || 0 }}</td>
                  <td>{{ homeData.totalOrder }}</td>
                </tr>
                <tr>
                  <th>交易额</th>
                  <td>{{ homeData.todayPay ? homeData.todayPay.toFixed(2) : '0.00' }}</td>
                  <td>{{ yesterdayData.pay ? yesterdayData.pay.toFixed(2) : '0.00' }}</td>
                  <td>{{ monthData.pay ? monthData.pay.toFixed(2) : '0.00' }}</td>
                  <td>{{ homeData.totalPay ? homeData.totalPay.toFixed(2) : '0.00' }}</td>
                </tr>
                <tr>
                  <th>新增会员</th>
                  <td>{{ homeData.todayUser }}</td>
                  <td>{{ yesterdayData.user || 0 }}</td>
                  <td>{{ monthData.user || 0 }}</td>
                  <td>{{ homeData.totalUser }}</td>
                </tr>
                <tr>
                  <th>活跃用户</th>
                  <td>{{ homeData.todayActiveUser }}</td>
                  <td>{{ yesterdayData.activeUser || 0 }}</td>
                  <td>{{ monthData.activeUser || 0 }}</td>
                  <td>{{ homeData.totalPayUser }}</td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="boxall" style="height: calc(33.333% - .15rem)">
            <div class="alltitle">
              用户分析
            </div>
            <div class="boxnav" id="echarts6" style="height:calc(100% - .3rem);">
              <commonChart 
                v-if="userAnalysisData.length > 0" 
                :title="userAnalysisChart.title" 
                :color="userAnalysisChart.color" 
                :chart-type="userAnalysisChart.chartType" 
                :head-list="userAnalysisChart.header" 
                :data-list="userAnalysisData" 
                width="100%" 
                id="chart6" 
                height="100%"
              />
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { getHomeData, getStatisticData } from "@/api/home";
import commonChart from '../components/charts/index';
import gradientChart from '../components/charts/gradientChart';

export default {
  name: "BigDataVisualization",
  components: {
    commonChart,
    gradientChart
  },
  data() {
    return {
      loading: true,
      currentTime: '',
      timeInterval: null,
      homeData: { 
        todayUser: 0, 
        totalUser: 0, 
        todayOrder: 0, 
        totalOrder: 0, 
        todayPay: 0, 
        totalPay: 0, 
        todayActiveUser: 0, 
        totalPayUser: 0 
      },
      yesterdayData: {},
      monthData: {},
      chart1: { 
        title: '近七日订单数量', 
        color: '#ff5b57', 
        chartType: 'bar', 
        header: ['订单统计'] 
      },
      chart2: { 
        title: '近七日会员活跃数', 
        color: '#00acac', 
        chartType: 'line', 
        header: ['会员统计'] 
      },
      paymentChart: { 
        title: '交易金额趋势', 
        color: '#fccb05', 
        chartType: 'line', 
        header: ['交易金额'] 
      },
      distributionChart: { 
        title: '数据分布', 
        color: '#8bd46e', 
        chartType: 'bar', 
        header: ['分布统计'] 
      },
      userAnalysisChart: { 
        title: '用户分析', 
        color: '#248ff7', 
        chartType: 'pie', 
        header: ['用户类型'] 
      },
      chartData1: [],
      chartData2: [],
      paymentData: [],
      distributionData: [],
      userAnalysisData: []
    };
  },
  created() {
    this.initTime();
    this.getHomeData();
    this.getChartsData();
    this.generateMockData();
  },
  beforeDestroy() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
  },
  methods: {
    // 初始化时间显示
    initTime() {
      this.updateTime();
      this.timeInterval = setInterval(this.updateTime, 1000);
    },
    // 更新时间
    updateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      this.currentTime = `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
    },
    // 格式化数字
    formatNumber(num) {
      if (!num) return '0';
      return (num / 10000).toFixed(2);
    },
    // 查询首页数据
    getHomeData() {
      getHomeData().then(response => {
        this.homeData = response.data;
      }).catch(() => {
        // 如果接口失败，使用模拟数据
        this.homeData = {
          todayUser: 156,
          totalUser: 12580,
          todayOrder: 89,
          totalOrder: 5680,
          todayPay: 25680.50,
          totalPay: 1256800.00,
          todayActiveUser: 234,
          totalPayUser: 8960
        };
      });
    },
    // 查询统计数据
    getChartsData() {
      getStatisticData({ tag: 'order,user_active' }).then(response => {
        const data = response.data;
        const labelData1 = data.data[0] ? data.data[0] : [];
        const labelData2 = data.data[1] ? data.data[1] : [];
        const dataList1 = [];
        const dataList2 = [];

        data.labels.forEach((label, index) => {
          const value1 = labelData1[index] ? labelData1[index] : 0;
          const value2 = labelData2[index] ? labelData2[index] : 0;
          dataList1.push({ name: label, value0: value1 });
          dataList2.push({ name: label, value0: value2 });
        });
        
        this.chartData1 = dataList1;
        this.chartData2 = dataList2;
        this.loading = false;
      }).catch(() => {
        // 如果接口失败，生成模拟数据
        this.generateMockChartData();
        this.loading = false;
      });
    },
    // 生成模拟图表数据
    generateMockChartData() {
      const labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      this.chartData1 = labels.map(label => ({
        name: label,
        value0: Math.floor(Math.random() * 100) + 20
      }));
      this.chartData2 = labels.map(label => ({
        name: label,
        value0: Math.floor(Math.random() * 200) + 50
      }));
    },
    // 生成其他模拟数据
    generateMockData() {
      // 交易金额数据
      const paymentLabels = ['1月', '2月', '3月', '4月', '5月', '6月'];
      this.paymentData = paymentLabels.map(label => ({
        name: label,
        value0: Math.floor(Math.random() * 50000) + 10000
      }));

      // 数据分布
      const distributionLabels = ['商品A', '商品B', '商品C', '商品D', '商品E'];
      this.distributionData = distributionLabels.map(label => ({
        name: label,
        value0: Math.floor(Math.random() * 500) + 100
      }));

      // 用户分析
      const userLabels = ['新用户', '老用户', 'VIP用户'];
      this.userAnalysisData = userLabels.map(label => ({
        name: label,
        value0: Math.floor(Math.random() * 1000) + 200
      }));

      // 昨日和本月数据
      this.yesterdayData = {
        order: 76,
        pay: 18960.30,
        user: 89,
        activeUser: 198
      };

      this.monthData = {
        order: 2340,
        pay: 568900.80,
        user: 1890,
        activeUser: 5670
      };
    },
    // 返回数据看板
    goBack() {
      this.$router.push({ path: '/index' });
    }
  }
};
</script>

<style scoped lang="scss">
@font-face {
  font-family: electronicFont;
  src: url('../../assets/fonts/DS-DIGIT.TTF');
}

.bigdata-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000d4a url('../../assets/images/bigdata/bg.jpg') center center;
  background-size: cover;
  color: #fff;
  font-size: 0.1rem;
  overflow: hidden;
  z-index: 9999;

  * {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    font-family: "微软雅黑";
  }

  li {
    list-style-type: none;
  }

  .clearfix:after, .clearfix:before {
    display: table;
    content: " ";
  }
  .clearfix:after {
    clear: both;
  }

  // 加载动画
  .loading {
    position: fixed;
    left: 0;
    top: 0;
    font-size: 16px;
    z-index: 100000000;
    width: 100%;
    height: 100%;
    background: #1a1a1c;
    text-align: center;

    .loadbox {
      position: absolute;
      width: 160px;
      height: 150px;
      color: #324e93;
      left: 50%;
      top: 50%;
      margin-top: -100px;
      margin-left: -75px;

      img {
        margin: 10px auto;
        display: block;
        width: 40px;
      }
    }
  }

  // 头部样式
  .head {
    height: 1.05rem;
    background: url('../../assets/images/bigdata/head_bg.png') no-repeat center center;
    background-size: 100% 100%;
    position: relative;

    .back-btn {
      position: absolute;
      left: 0.2rem;
      top: 50%;
      transform: translateY(-50%);
      color: rgba(255, 255, 255, 0.9);
      font-size: 0.16rem;
      padding: 0.1rem 0.2rem;
      border-radius: 0.2rem;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
      z-index: 10;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        color: #fff;
      }

      i {
        margin-right: 0.05rem;
      }
    }

    h1 {
      color: #fff;
      text-align: center;
      font-size: 0.4rem;
      line-height: 0.8rem;
      letter-spacing: -1px;

      img {
        width: 1.5rem;
        display: inline-block;
        vertical-align: middle;
      }
    }

    .time {
      position: absolute;
      right: 0.15rem;
      top: 0;
      line-height: 0.75rem;
      color: rgba(255, 255, 255, 0.7);
      font-size: 0.3rem;
      padding-right: 0.1rem;
      font-family: electronicFont;
    }
  }

  // 主体内容
  .mainbox {
    padding: 0 0.2rem 0rem 0.2rem;
    height: calc(100% - 1.05rem);

    > ul {
      margin-left: -0.1rem;
      margin-right: -0.1rem;
      height: 100%;

      > li {
        float: left;
        padding: 0 0.1rem;
        height: 100%;
        width: 30%;

        &:nth-child(2) {
          width: 40%;
        }
      }
    }
  }

  // 盒子样式
  .boxall {
    padding: 0 0.2rem 0.2rem 0.2rem;
    background: rgba(6, 48, 109, 0.5);
    position: relative;
    margin-bottom: 0.15rem;
    z-index: 10;

    .alltitle {
      font-size: 0.2rem;
      color: #fff;
      line-height: 0.5rem;
      position: relative;
      padding-left: 0.15rem;

      &:before {
        position: absolute;
        height: 0.2rem;
        width: 4px;
        background: #49bcf7;
        border-radius: 5px;
        content: "";
        left: 0;
        top: 50%;
        margin-top: -0.1rem;
      }
    }

    .boxnav {
      height: calc(100% - 0.5rem);
    }
  }

  // 行列布局
  .row > li {
    float: left;
    height: 100%;
  }
  .col-6 {
    width: 50%;
  }
  .col-3 {
    width: 25%;
  }
  .col-4 {
    width: 33.33333%;
  }
  .h100 {
    height: 100%;
  }

  // 标题样式
  .tit01 {
    text-align: center;
    color: white;
    font-size: 0.16rem;
    padding: 0.3rem 0 0.02rem 0;
  }

  // 饼图容器
  .piebox {
    height: calc(100% - 0.5rem);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    &:before {
      width: 0.6rem;
      height: 0.6rem;
      content: "";
      border: 1px solid #49bcf7;
      border-radius: 1rem;
      position: absolute;
      left: 50%;
      top: 50%;
      margin-left: -0.31rem;
      margin-top: -0.31rem;
      opacity: 0.7;
    }

    .pie-number {
      font-size: 0.24rem;
      color: #49bcf7;
      font-weight: bold;
      z-index: 1;
    }
  }

  // 业绩总览
  .sqzs {
    margin-right: 0.2rem;

    p {
      padding: 0.2rem 0 0.1rem 0;
      font-size: 0.22rem;
    }

    h1 {
      height: calc(100% - 0.65rem);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      color: #fef000;
      font-weight: normal;
      letter-spacing: 2px;
      font-size: 0.5rem;
      justify-content: center;
      padding-bottom: 0.05rem;

      span {
        font-size: 0.8rem;
        font-family: Impact, Haettenschweiler, 'Arial Narrow Bold', sans-serif;
      }
    }
  }

  // 表格样式
  table {
    width: 100%;
    text-align: center;

    th {
      font-size: 0.16rem;
      background: rgba(0, 0, 0, 0.1);
    }

    td {
      font-size: 0.16rem;
      color: rgba(255, 255, 255, 0.6);
    }

    th, td {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 0.1rem 0;
    }
  }
}

// 响应式字体大小
html {
  font-size: calc(100vw / 20);
}

@media screen and (max-width: 1200px) {
  html {
    font-size: calc(100vw / 15);
  }
}

@media screen and (max-width: 768px) {
  html {
    font-size: calc(100vw / 10);
  }
}
</style>
