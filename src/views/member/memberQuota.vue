<template>
  <div class="app-container">
    <div class="main-panel">
      <el-form  :model="queryParams" class="main-search" ref="queryForm" size="small" v-show="showSearch" :inline="true"  label-width="68px">
        <el-form-item label="记录ID" prop="userId">
          <el-input
              v-model="queryParams.id"
              placeholder="请输入记录ID"
              clearable
              style="width: 240px;"
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="会员等级" prop="gradeId">
          <el-select
              v-model="queryParams.gradeId"
              clearable
              placeholder="请选择会员等级"
              style="width: 100%"
          >
            <el-option
                v-for="grade in gradeOptions"
                :key="grade.id"
                :label="`${grade.name}`"
                :value="grade.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="门店" prop="storeId">
          <el-select
              v-model="queryParams.storeId"
              clearable
              :loading="storeLoading"
              placeholder="请选择门店"
              style="width: 100%"
          >
            <el-option
                v-for="storeId in storeOptions"
                :key="storeId.id"
                :label="`${storeId.name}`"
                :value="storeId.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
              v-model="queryParams.status"
              placeholder="状态"
              clearable
          >
            <el-option key="A" label="有效" value="A"/>
            <el-option key="N" label="无效" value="N"/>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button
              v-hasPermi="['goods:cate:index']"
              icon="el-icon-plus"
              plain
              size="mini"
              type="primary"
              @click="handleAdd"
          >新增
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table ref="tables" v-loading="loading" :data="list" :default-sort="defaultSort" @sort-change="handleSortChange">
      <el-table-column label="ID" prop="id" width="40"/>
      <el-table-column :formatter="formatGrade" align="center" label="会员等级" prop="gradeId"/>
      <el-table-column :formatter="formatStoreId" align="center" label="门店" prop="storeId"/>
      <el-table-column align="center" label="售价" prop="price"/>
      <el-table-column align="center" label="总名额" prop="totalQuota"/>
      <el-table-column align="center" label="已使用名额" prop="usedQuota"/>
      <el-table-column align="center" label="赠送的优惠券" prop="giveCouponId"/>
      <el-table-column align="center" label="排序" prop="sortOrder" width="50"/>
      <el-table-column :formatter="formatStatus" align="center" label="状态" prop="status"/>
      <el-table-column align="center" label="创建时间" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="更新时间" prop="updateTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
        <template slot-scope="scope">
          <el-button
              v-hasPermi="['goods:cate:index']"
              icon="el-icon-edit"
              size="mini"
              type="text"
              @click="handleUpdate(scope.row)"
          >修改
          </el-button>
          <el-button
              v-hasPermi="['goods:cate:index']"
              icon="el-icon-delete"
              size="mini"
              type="text"
              @click="handleDelete(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.page"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body class="common-dialog" width="700px">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="门店" prop="storeId">
              <el-select
                  v-model="form.storeId"
                  :loading="storeLoading"
                  clearable
                  placeholder="请选择门店"
                  style="width: 100%"
              >
                <el-option
                    v-for="store in storeOptions"
                    :key="store.id"
                    :label="`${store.name}`"
                    :value="store.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="会员等级" prop="gradeId">
              <el-select
                  v-model="form.gradeId"
                  clearable
                  placeholder="请选择会员等级"
                  style="width: 100%"
              >
                <el-option
                    v-for="grade in gradeOptions"
                    :key="grade.id"
                    :label="`${grade.name}`"
                    :value="grade.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="售价（元）" prop="price">
              <el-input v-model="form.price" placeholder="请输入售价"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="总名额" prop="totalQuota">
              <el-input v-model="form.totalQuota" placeholder="请输入总名额"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="已使用名额" prop="usedQuota">
              <el-input v-model="form.usedQuota" placeholder="请输入已使用名额"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="赠送的优惠券ID" prop="giveCouponId">
              <el-input v-model="form.giveCouponId" placeholder="请输入赠送的优惠券ID"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="排序" prop="sortOrder">
              <el-input v-model="form.sortOrder" placeholder="请输入排序，数字小优先"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="状态(默认启用)" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio key="A" label="A" value="A">启用</el-radio>
                <el-radio key="I" label="I" value="I">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {
  deleteGradeQuota,
  getAllMemberGrades,
  getAllStore,
  getGradeQuotaInfo,
  getGradeQuotaList,
  saveGradeQuota
} from "@/api/memberQuota";

export default {
  name: "MemberQuota",
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
      // 门店搜索加载状态
      storeLoading: false,
      // 门店选项列表
      storeOptions: [],
      // 会员等级搜索加载状态
      gradeLoading: false,
      // 会员等级选项列表
      gradeOptions: [],
      // 遮罩层
      loading: true,
      // 标题
      title: "",
      // 选中数组
      ids: [],
      // // 非多个禁用
      // multiple: true,
      // // 显示搜索条件
      // showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // // 升级条件
      // catchTypeList: [],
      // 是否显示弹出层
      open: false,
      // 默认排序
      defaultSort: {prop: 'createTime', order: 'descending'},
      // 表单参数
      form: {
        id: '',
        gradeId: 0,
        storeId: '',
        price: '',
        totalQuota: '',
        usedQuota: '',
        giveCouponId: '',
        sortOrder: '',
        status: '',
        createTime: '',
        updateTime: ''
      },
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        name: '',
        catchType: '',
        id: '',
        gradeId: '',
        storeId: '',
        status: ''
      },
      // 表单校验
      rules: {
        gradeId: [
          {
            required: true,
            message: "等级ID不能为空",
            trigger: "blur"
          },
          {
            pattern: /^(1|86|87)$/,
            message: '等级ID只能是1, 86, 87',
            trigger: 'blur'
          }
        ],
        storeId: [
          {
            required: true,
            message: "门店ID不能为空",
            trigger: "blur"
          },
          {
            validator: (rule, value, callback) => {
              if (!/^[1-9]\d*$/.test(value)) {
                callback(new Error('必须输入正整数'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        price: [
          {
            required: true,
            message: "售价不能为空",
            trigger: "blur"
          },
          {
            validator: (rule, value, callback) => {
              const numValue = parseFloat(value);
              if (isNaN(numValue) || numValue <= 0) {
                callback(new Error('必须大于0的正数'));
              } else if (!/^\d+(\.\d{1,2})?$/.test(value)) {
                callback(new Error('最多保留两位小数'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        totalQuota: [
          {
            required: true,
            message: "总名额不能为空",
            trigger: "blur"
          },
          {
            validator: (rule, value, callback) => {
              const numValue = parseInt(value);
              if (isNaN(numValue) || numValue <= 0) {
                callback(new Error('必须输入大于0的整数'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        usedQuota: [
          {
            required: true,
            message: "已使用名额不能为空",
            trigger: "blur"
          },
          {
            validator: (rule, value, callback) => {
              // 确保是数字
              if (isNaN(Number(value))) {
                callback(new Error('必须输入数字'));
                return;
              }

              const used = parseInt(value);
              const total = parseInt(this.form.totalQuota);

              // 验证不能大于总名额
              if (used > total) {
                callback(new Error('已使用名额不能大于总名额'));
              } else if (used < 0) {
                callback(new Error('已使用名额不能小于0'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        giveCouponId: [
          {
            required: true,
            message: "赠送的优惠券ID不能为空",
            trigger: "blur"
          },
          {
            validator: (rule, value, callback) => {
              if (!/^[1-9]\d*$/.test(value)) {
                callback(new Error('必须输入正整数'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        sortOrder: [
          {
            required: true,
            message: "排序值不能为空",
            trigger: "blur"
          },
          {
            validator: (rule, value, callback) => {
              if (!/^[1-9]\d*$/.test(value)) {
                callback(new Error('必须输入正整数'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],

      }
    };
  },
  created() {
    this.getList();
    // 获取所有门店
    this.getAllStore();
    //获取所有会员等级
    this.getAllMemberGrades();
  },
  // activated() {
  //   this.getSettingInfo();
  // },
  methods: {
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    // 重置按钮操作
    resetQuery() {
      this.queryParams = {
        page: 1,
        pageSize: 10,
        id: '',
        gradeId: '',
        storeId: '',
        status: ''
      };
      this.resetForm("queryForm");
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order);
      this.handleQuery();
    },
    // 格式化门店ID为门店名称
    formatStoreId(row, column) {
      const storeId = row.storeId;
      const store = this.storeOptions.find(s => s.id === storeId);
      return store ? store.name : `未知门店(${storeId})`;
    },
    // 会员等级ID为会员等级名称
    formatGrade(row, column) {
      const gradeId = row.gradeId;
      const grade = this.gradeOptions.find(s => s.id === gradeId);
      return grade ? grade.name : `未知门店(${gradeId})`;
    },
    // 状态显示更改
    formatStatus(row, column) {
      const statusMap = {
        'A': '启用',
        'I': '禁用'
      };
      return statusMap[row.status] || row.status;
    },
    // 搜索门店
    getAllStore() {
      this.storeLoading = true;
      getAllStore().then(response => {
        if (response.data &&
            response.data.paginationResponse &&
            response.data.paginationResponse.content) {
          this.storeOptions = response.data.paginationResponse.content;

        } else {
          this.$message.error('获取门店列表失败');
          console.error('无效的响应结构:', response);
        }
      }).catch(error => {
        console.error("获取门店失败:", error);
        this.$message.error('门店加载失败');
      }).finally(() => {
        this.storeLoading = false;
      });
    },
    // 获取所有会员等级
    getAllMemberGrades() {
      this.gradeLoading = true;
      getAllMemberGrades().then(response => {
        if (response.data && response.data.paginationResponse && response.data.paginationResponse.content) {
          this.gradeOptions = response.data.paginationResponse.content;
        } else {
          this.$message.error('获取会员等级列表失败');
        }
      }).catch(error => {
        console.error("获取会员等级失败:", error);
        this.$message.error('会员等级加载失败');
      }).finally(() => {
        this.gradeLoading = false;
      });
    },

    // 分页查询规则列表
    getList() {
      this.loading = true;
      getGradeQuotaList(this.queryParams).then(response => {
        this.list = response.data.content;
        this.total = response.data.totalElements;
        this.loading = false;
      }).catch(error => {
        this.loading = false;
        this.$message.error('数据加载失败: ' + error.message);
      });
    },

    // 取消按钮
    cancel() {
      this.$store.dispatch('tagsView/delView', this.$route)
      // this.$router.push( { path: '/point/setting' } );
      this.open = false;
    },
    // 提交按钮
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id) {
            saveGradeQuota(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            saveGradeQuota(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 排序触发事件
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getGradeQuotaInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "编辑会员规则";
      });
    },
    // 删除按钮操作
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除"' + id + '"的数据项？').then(function () {
        console.log(id)
        return deleteGradeQuota(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    // 新增按钮操作
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增会员规则";
    },
    // 表单重置
    reset() {
      this.form = {
        id: "",
        gradeId: "",
        storeId: "",
        price: "",
        totalQuota: "",
        usedQuota: "",
        giveCouponId: "",
        sortOrder: "",
        status: "",
        createTime: "",
        updateTime: ""
      };
      this.resetForm("form");
    },
  }
};
</script>
<style lang="scss" rel="stylesheet/scss">
.main-panel {
  .content {
    margin-left: 50px;
    margin-top: 30px;
  }

  .footer {
    margin-top: 10px;
    margin-left: 250px;
  }
}
</style>
