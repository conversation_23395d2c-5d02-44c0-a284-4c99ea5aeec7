<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="main-search" label-width="68px"
             size="small">
      <el-form-item label="说明备注" prop="description">
        <el-input
          v-model="queryParams.description"
          clearable
          placeholder="请输入说明备注"
          style="width: 180px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作类型" prop="type">
        <el-select
          v-model="queryParams.type"
          clearable
          placeholder="操作类型"
          style="width: 100px"
        >
          <el-option key="increase" label="入库" value="increase"/>
          <el-option key="reduce" label="出库" value="reduce"/>
        </el-select>
      </el-form-item>
      <el-form-item label="所属店铺" prop="store">
        <el-select
          v-model="queryParams.storeId"
          clearable
          placeholder="所属店铺"
          style="width: 180px"
        >
          <el-option v-if="!this.$store.getters.storeId" :key="0" :value="0" label="公共所有"/>
          <el-option v-for="storeInfo in storeOptions" :key="storeInfo.id" :label="storeInfo.name"
                     :value="storeInfo.id"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button
          v-hasPermi="['stock:save']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >新增入库
        </el-button>
        <el-button
          v-hasPermi="['stock:save']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="common"
          @click="handleReduce"
        >新增出库
        </el-button>
      </el-form-item>
    </el-form>

    <el-table ref="tables" v-loading="loading" :data="list" :default-sort="defaultSort"
              @selection-change="handleSelectionChange" @sort-change="handleSortChange">
      <el-table-column label="ID" prop="id" width="80"/>
      <el-table-column align="center" label="类型" prop="type" width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.type == 'reduce'">出库</span>
          <span v-if="scope.row.type == 'increase'">入库</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="所属店铺">
        <template slot-scope="scope">
          <span v-if="scope.row.storeId && scope.row.storeId > 0">
              <span>{{ getName(storeOptions, scope.row.storeId) }}</span>
          </span>
          <span v-else>公共所有</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="说明备注" prop="description">
        <template slot-scope="scope">
          <span>{{ scope.row.description ? scope.row.description : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建时间" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="更新时间" prop="updateTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center"  label="状态" prop="reviewStatus">
        <template slot-scope="scope">
          <el-tag
            :type="
        scope.row.reviewStatus === '已通过'
          ? 'success'
          : scope.row.reviewStatus === '待审核'
            ? 'warning'
            : scope.row.reviewStatus === '未通过'
              ? 'danger'
              : 'info'
      "
          >
            {{ scope.row.reviewStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="审核说明" prop="description">
        <template slot-scope="scope">
          <span>{{ scope.row.reviewDesc ? scope.row.reviewDesc : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['stock:edit']"
            :disabled="storeId != scope.row.storeId && storeId != 0"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >编辑
          </el-button>
          <el-button
            v-hasPermi="['stock:reviewEdit']"
            :disabled="  scope.row.reviewStatus !== '未通过'"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >再次审核
          </el-button>
          <el-button
            v-hasPermi="['stock:detail']"
            icon="el-icon-view"
            size="mini"
            type="text"
            @click="handleDetail(scope.row)"
          >详情
          </el-button>
          <el-button
            :disabled="scope.row.reviewStatus !== '待审核'"
            v-hasPermi="['stock:review']"
            icon="el-icon-check"
            size="mini"
            type="text"
            @click="handleReview(scope.row)"
          >审核
          </el-button>
          <el-button
            v-if="scope.row.reviewStatus !== '已通过'"
            v-hasPermi="['stock:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.page"
      :total="total"
      @pagination="getList"
    />
    <!--添加商品库存-->
    <el-dialog :title="title" :visible.sync="open" append-to-body class="common-dialog" width="80%">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="所属店铺" prop="storeId">
              <el-select value-key="value" v-model="form.storeId" class="input" clearable placeholder="请选择所属店铺">
<!--                <el-option v-if="storeId == 0" :key="0" :value="0" label="公共所有"/>-->
                <el-option
                  v-for="item in storeOptions"
                  :key="item.id"
                  :disabled="item.status !== 'A'"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
<!--              <div class="form-tips">提示：未选择则属于公共所有</div>-->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息">
              <el-input v-model="form.description" placeholder="请输入内容" rows="3" type="textarea"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="button-group mb5">
        <el-button v-if="!isView" size="mini" type="primary" @click="selectGoods()">添加商品</el-button>
        <el-button v-if="!isView" size="mini" type="primary" @click="addInboundOrder()">
          {{ form.type === 'increase' ? '添加入库单' : '添加出库单' }}
        </el-button>
        <!-- 添加图片预览区域 -->
        <el-text>{{ form.type === 'increase' ? '已上传的入库单：' : '已上传的出库单：' }}</el-text>
        <div v-if="form.stockUrl" class="image-preview-container">
          <el-image
            :preview-src-list="[imagePath + form.stockUrl]"
            :src="imagePath + form.stockUrl"
            style="width: 80px; height: 80px; margin-left: 10px;"
          ></el-image>
        </div>
      </div>
      <el-table ref="goodsTables" v-loading="loading" :data="goodsList" border>
        <el-table-column label="商品条码" prop="goodsNo"/>
        <el-table-column align="center" label="主图" width="100">
          <template slot-scope="scope">
            <img :src="imagePath + scope.row.logo" class="list-img">
          </template>
        </el-table-column>
        <el-table-column align="center" label="商品名称" prop="name">
          <template slot-scope="scope">
            <span>{{ scope.row.name ? scope.row.name : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="商品规格" prop="spec">
          <template slot-scope="scope">
           <span v-if="scope.row.specList">
              <span v-for="spec in scope.row.specList" class="spec-item">{{ spec.value }}</span>
           </span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="数量" prop="type">
          <template slot-scope="scope">
            <el-input-number v-model="scope.row.num" :disabled="isView" :min="0" style="width: 150px"/>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['goods:goods:index']"
              :disabled="isView"
              icon="el-icon-delete"
              size="mini"
              type="text"
              @click="deleteGoods(scope.row)"
            >删除
            </el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" label="损耗证明">
          <template slot-scope="scope"  >
            <!-- 详情状态下显示预览 -->
            <template v-if="isView">
              <el-image
                v-if="scope.row.lossUrl"
                :src="imagePath + scope.row.lossUrl"
                :preview-src-list="[imagePath + scope.row.lossUrl]"
                style="width: 30px; height: 60px;"
                fit="cover"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" style="font-size: 20px;"></i>
                </div>
              </el-image>
              <span v-else>--</span>
            </template>
            <!-- 编辑状态下显示上传按钮 -->
            <template v-else>
              <div style="position: relative; display: inline-block;">
                <el-upload
                  :action="uploadAction"
                  :headers="uploadHeader"
                  :limit="1"
                  :on-success="(res, file, fileList) => handleLossUploadSuccess(res, scope.$index)"
                  :show-file-list="false"
                  class="form__head-icon-upload"
                >
                  <el-image
                    v-if="scope.row.lossUrl"
                    :src="imagePath + scope.row.lossUrl"
                    fit="cover"
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                  <div v-else class="upload-placeholder">
                    <i class="el-icon-plus"></i>
                  </div>
                </el-upload>

                <!-- 添加删除按钮（仅在已有图片时显示） -->
                <el-button
                  v-if="scope.row.lossUrl"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  size="mini"
                  style="position: absolute; top: -5px; right: -5px;"
                  @click.stop="removeLossImage(scope.$index)"
                ></el-button>
              </div>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!isView" type="primary" @click="submitForm">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-dialog>

    <!--审核对话框-->
    <el-dialog :visible.sync="reviewOpen" append-to-body class="common-dialog" title="库存审核" width="80%">
      <el-form ref="reviewForm" :model="reviewForm" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="所属店铺">
              <span v-if="reviewForm.storeId && reviewForm.storeId > 0">
                {{ getName(storeOptions, reviewForm.storeId) }}
              </span>
              <span v-else>公共所有</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="说明备注">
              <span>{{ reviewForm.description || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :rules="[{ required: isReject, message: '驳回必须填写审核说明', trigger: 'blur' }]" label="审核说明" prop="reviewDesc"
                          required>
              <!-- 添加输入框用于填写驳回说明 -->
              <el-input
                v-model="reviewForm.reviewDesc"
                :disabled="isView"
                placeholder="请输入审核说明（驳回时必填）"
                rows="3"
                type="textarea"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="reviewForm.stockUrl">
          <el-col :span="24">
            <el-form-item label="入库单">
              <div style="display: flex; flex-wrap: wrap;">
                <el-image
                  :preview-src-list="[imagePath + reviewForm.stockUrl]"
                  :src="imagePath + reviewForm.stockUrl"
                  style="width: 100px; height: 100px; margin-right: 10px; margin-bottom: 10px;"
                ></el-image>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>


      <!-- 商品列表 -->
      <el-table v-loading="reviewLoading" :data="reviewGoodsList" border>
        <el-table-column label="商品条码" prop="goodsNo"/>
        <el-table-column align="center" label="主图" width="100">
          <template slot-scope="scope">
            <img :src="imagePath + scope.row.logo" class="list-img">
          </template>
        </el-table-column>
        <el-table-column align="center" label="商品名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="商品规格">
          <template slot-scope="scope">
           <span v-if="scope.row.specList">
              <span v-for="spec in scope.row.specList" class="spec-item">{{ spec.value }}</span>
           </span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="数量" prop="type">
          <template slot-scope="scope">
            <span>{{ scope.row.num }}</span>
          </template>
        </el-table-column>
        <!-- 损耗证明列 -->
        <el-table-column label="损耗证明" align="center" >
          <template slot-scope="scope" >
            <el-image
              v-if="scope.row.lossUrl"
              :src="imagePath + scope.row.lossUrl"
              :preview-src-list="[imagePath + scope.row.lossUrl]"
              style="width: 60px; height: 60px;"
              fit="cover"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline" style="font-size: 20px;"></i>
              </div>
            </el-image>
            <span v-else>--</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.page"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="passReview">通过</el-button>
        <el-button type="danger" @click="rejectReview">驳回</el-button>
        <el-button @click="reviewOpen = false">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 选择商品组件 start-->
    <selectGoodsDialog
      :dataList="goodsList"
      :show-dialog="openSelectGoodsDialog"
      :storeId="form.storeId"
      @closeDialog="closeSelectGoods"
      @submit="doSelectGoods"
    ></selectGoodsDialog>
    <!-- 选择商品组件 end-->
    <!-- 添加入库单组件 start-->
    <addInboundOrderDialog
      :imagePath="imagePath"
      :show-dialog="openAddInboundOrderDialog"
      @closeDialog="closeInboundOrder"
      @submit="handleInboundOrderSubmit"
    ></addInboundOrderDialog>
    <!-- 选择商品组件     :storeId="form.storeId"
      :dataList="goodsList"
           @submit="doSelectGoods"end-->

  </div>
</template>

<script>
import {audit, deleteStock, getStockInfo, getStockList, saveStock} from "@/api/stock";
import selectGoodsDialog from './selectGoodsDialog';
import AddInboundOrderDialog from "@/views/stock/addInboundOrderDialog.vue";
import {getToken} from "@/utils/auth";

export default {
  name: "StockIndex",
  components: {
    AddInboundOrderDialog,
    selectGoodsDialog
  },
  data() {
    return {
      // 添加上传相关配置
      uploadAction: process.env.VUE_APP_SERVER_URL + 'backendApi/file/upload',
      uploadHeader: {'Access-Token': getToken()},
      // 用于标记当前是否为驳回操作
      isReject: false,
      reviewOpen: false,         // 审核对话框可见性
      reviewLoading: false,      // 审核加载状态
      reviewForm: {},            // 审核表单数据
      reviewGoodsList: [],       // 审核商品列表
      isView: false,
      openSelectGoodsDialog: false,
      openAddInboundOrderDialog: false,
      storeId: this.$store.getters.storeId,
      // 遮罩层
      loading: true,
      // 标题
      title: "",
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 图片根目录
      imagePath: "",
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      goodsList: [],
      // 店铺列表
      storeOptions: [],
      // 是否显示弹出层
      open: false,
      // 默认排序
      defaultSort: {prop: 'sort', order: 'descending'},
      // 表单参数
      form: {
        storeId: this.$store.getters.storeId,
        type: 'increase',
        id: '',
        description: '',
        stockUrl: '',
        lossUrl: '',
        reviewDesc: '',
        status: "A",
        goodsList: []
      },
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        description: '',
        type: ''
      },
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();

  },
  methods: {

    // 损耗图片上传成功处理
    handleLossUploadSuccess(res, index) {
      if (res.code === 200) {
        this.$set(this.goodsList, index, {
          ...this.goodsList[index],
          lossUrl: res.data.fileName // 假设后端返回fileName
        })
        this.$message.success("上传成功")
      } else {
        this.$message.error("上传失败")
      }
    },

    removeLossImage(index) {
      this.$set(this.goodsList, index, {
        ...this.goodsList[index],
        lossUrl: null
      })
    },

    // 上传前检查
    beforeLossUpload(file) {
      const isImage = file.type.indexOf('image/') === 0;
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error('请上传图片格式文件!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过2MB!');
      }

      return isImage && isLt2M;
    },


    // 查询分类列表
    getList() {
      this.loading = true;

      getStockList(this.queryParams).then(response => {
          this.list = response.data.paginationResponse.content;
          this.total = response.data.paginationResponse.totalElements;
          this.imagePath = response.data.imagePath;
          this.storeOptions = response.data.storeList;
          this.loading = false;
        }
      );
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    // 重置按钮操作
    resetQuery() {
      this.resetForm("queryForm");
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
      this.handleQuery();
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.status == "A" ? "启用" : "禁用";
      this.$modal.confirm('确认要' + text + '"' + row.name + '"吗？').then(function () {
        return deleteStock(row.id, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function () {
        row.status = row.status === "N" ? "A" : "N";
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    // 排序触发事件
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    // 新增入库按钮操作
    handleAdd() {
      this.reset();
      this.isView = false;
      this.open = true;
      this.form.type = 'increase';
      this.title = "新增入库";
      if (this.storeId && this.storeId !== 0) {
        this.form.storeId = this.storeId;
      }
    },
    // 新增出库按钮操作
    handleReduce() {
      this.reset();
      this.isView = false;
      this.open = true;
      this.form.type = 'reduce';
      this.title = "新增出库";
      if (this.storeId && this.storeId !== 0) {
        this.form.storeId = this.storeId;
      }
    },
    // 表单重置
    reset() {
      this.form = {
        id: "",
        storeId: "",
        status: "A",
        description: "",
        goodsList: [],
        // 入库单图片
        stockUrl: ''
      };
      this.goodsList = [];
      this.resetForm("form");
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 添加入库单弹窗
    addInboundOrder() {
      this.openAddInboundOrderDialog = true;
    },
    closeInboundOrder() {
      this.openAddInboundOrderDialog = false;
    },
    // 处理入库单提交事件
    handleInboundOrderSubmit(images) {
      // 只取第一张图片（单张上传）
      if (images && images.length > 0) {
        this.form.stockUrl = images[0].fileName;
      } else {
        this.form.stockUrl = "";
      }
      this.openAddInboundOrderDialog = false;
    },
    // 选择商品弹窗
    selectGoods() {
      this.openSelectGoodsDialog = true;
    },
    closeSelectGoods() {
      this.openSelectGoodsDialog = false;
    },
    doSelectGoods(selectData) {
      const app = this;
      app.openSelectGoodsDialog = false;
      app.goodsList = selectData;
      app.goodsList.forEach(function (goods, key) {
        if (!goods.num) {
          app.$set(app.goodsList[key], 'num', 1);
        }
        // 初始化lossUrl属性
        if (!goods.lossUrl) {
          app.$set(app.goodsList[key], 'lossUrl', null);
        }
      })
    },
    // 删除商品操作
    deleteGoods(row) {
      const dataList = [];
      this.goodsList.forEach(function (item) {
        if (item.id != row.id || item.skuId != row.skuId) {
          dataList.push(item);
        }
      })
      this.goodsList = dataList;
    },
    // 提交按钮
    submitForm: function () {
      const app = this;
      app.$refs["form"].validate(valid => {
        if (valid && !app.loading) {
          if (!app.goodsList || app.goodsList.length < 1) {
            app.$modal.alert("请先添加商品");
            return false;
          }
          // 只有入库操作需要强制上传
          if (app.form.type === 'increase' && !app.form.stockUrl) {
            app.$modal.alert("入库操作必须上传入库单");
            return false;
          }
          // 编辑时将状态设置为"待审核"
          if (this.form.id) {
            this.form.reviewStatus = "待审核";
          }

          app.form.goodsList = app.goodsList.map(item => {
            return {
              id: item.id,
              goodsId: item.goodsId,
              skuId: item.skuId,
              num: item.num,
              lossUrl: item.lossUrl
            };
          });
          app.form.goodsList = app.goodsList;
          app.loading = true;
          saveStock(this.form).then(response => {
            const action = app.form.id ? "修改" : "新增";
            app.$modal.msgSuccess(action + "成功");
            app.open = false;
            app.getList();
            app.reset();
            app.loading = false;
          }).catch(() => {
            app.loading = false;
          });
        }
      });
    },
    // 编辑按钮操作
    handleUpdate(row) {
      this.reset();
      this.isView = false;
      const id = row.id;
      // 获取详情
      getStockInfo(id).then(response => {
        this.form = response.data.stockInfo;
        this.form.type = row.type;
        this.goodsList = response.data.goodsList;
        this.form.reviewStatus = "待审核";
        this.open = true;
        this.title = "修改" + (this.form.type === 'increase' ? '入库' : '出库') + "记录";
      });
    },

    // 详情按钮操作
    // handleDetail(row) {
    //    this.reset();
    //    this.isView = true;
    //    const id = row.id;
    //     getStockInfo(id).then(response => {
    //         this.form = response.data.stockInfo;
    //         this.goodsList = response.data.goodsList;
    //         this.open = true;
    //         this.title = "记录详情";
    //     });
    // },
    handleDetail(row) {
      this.reset();
      this.isView = true;
      const id = row.id;
      getStockInfo(id).then(response => {
        this.form = response.data.stockInfo;
        this.goodsList = response.data.goodsList;
        this.open = true;
        this.title = "记录详情";
      });
    },
    // 审核按钮点击处理
    handleReview(row) {
      this.reviewLoading = true;
      getStockInfo(row.id).then(response => {
        this.reviewForm = {...response.data.stockInfo};
        this.reviewGoodsList = response.data.goodsList || [];
        this.reviewOpen = true;
        this.reviewLoading = false;
      }).catch(() => {
        this.reviewLoading = false;
        this.$modal.msgError("加载审核数据失败");
      });
    },

    // 通过审核
    passReview() {
      this.submitReview('已通过');
    },

    // 驳回审核
    rejectReview() {
      this.isReject = true;
      // 手动触发表单验证
      this.$nextTick(() => {
        this.$refs['reviewForm'].validate((valid) => {
          if (valid) {
            this.submitReview('未通过');
          } else {
            this.$modal.msgError("请填写审核说明");
          }
        });
      });
    },

    // 提交审核结果
    submitReview(status) {
      const app = this;
      if (!app.reviewForm.id) {
        app.$modal.msgError("审核数据异常");
        return;
      }
      app.reviewLoading = true;
      // 构造与保存时一致的数据结构
      const reviewData = {
        ...app.reviewForm,
        goodsList: app.reviewGoodsList,
        reviewStatus: status
      };
      audit(reviewData).then(() => {
        const action = status === '已通过' ? "通过" : "驳回";
        app.$modal.msgSuccess(`审核${action}成功`);
        app.reviewOpen = false;
        app.getList();
      }).catch(() => {
        app.reviewLoading = false;
        app.$modal.msgError("审核操作失败");
      });
    },
    // 删除按钮操作
    handleDelete(row) {
      const name = row.id
      this.$modal.confirm('是否确认删除"' + name + '"的数据项？').then(function () {
        return deleteStock(row.id, 'D');
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    }
  }
};
</script>
<style lang="scss" scoped>
//.common-dialog > > > .el-upload--picture-card {
//  width: 60px;
//  height: 50px;
//  line-height: 60px;
//}
//.form__head-icon-upload{
//  width: 50px;
//  height: 50px;
//}
.button-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  .el-button {
    margin-right: 10px;
  }

  .image-preview-container {
    display: flex;
    margin-left: 10px;
  }
}
</style>

