<template>
  <div class="app-container">

      <el-form  :model="queryParams" class="main-search" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="记录ID" prop="userId">
          <el-input
            v-model="queryParams.id"
            placeholder="请输入记录ID"
            clearable
            style="width: 240px;"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="门店" prop="storeId">
          <el-select
            v-model="queryParams.storeId"
            clearable
            :loading="storeIdLoading"
            placeholder="请选择门店"
            style="width: 100%"
          >
            <el-option
              v-for="storeId in storeIdOptions"
              :key="storeId.id"
              :label="`${storeId.name}`"
              :value="storeId.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="会员等级" prop="gradeId">
          <el-select
            v-model="queryParams.gradeId"
            clearable
            placeholder="请选择会员等级"
            style="width: 100%"
          >
            <el-option
              v-for="grade in gradeOptions"
              :key="grade.id"
              :label="`${grade.name}`"
              :value="grade.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="状态"
            clearable
          >
            <el-option key="A" label="有效" value="A"/>
            <el-option key="N" label="无效" value="N"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['goods:cate:index']"
          >新增</el-button>
        </el-form-item>
      </el-form>
<!--      <div slot="footer" class="footer">-->
<!--        <el-button type="primary" @click="submitForm">保 存</el-button>-->
<!--        <el-button @click="cancel">取消</el-button>-->
<!--      </div>-->


    <el-table ref="tables" v-loading="loading" :data="list" :default-sort="defaultSort" @sort-change="handleSortChange">
      <el-table-column label="ID" width="55" prop="id"/>
      <el-table-column label="门店" align="center" prop="storeId"  :formatter="formatStoreId"/>
      <el-table-column label="会员等级" align="center" prop="gradeId" :formatter="formatGrade"/>
      <el-table-column label="最小消费金额（含）" align="center"  prop="minAmount"/>
      <el-table-column label="最大消费金额（不含）" align="center" prop="maxAmount" :formatter="formatMaxAmount"/>
      <el-table-column label="返利百分比" align="center" prop="rebateRate" />
      <el-table-column label="规则描述" align="center" prop="description" :formatter="formatDescription"/>
      <el-table-column label="状态" align="center" prop="status" :formatter="formatStatus"/>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="状态" align="center" prop="status">-->
<!--        <template slot-scope="scope">-->
<!--          <el-switch-->
<!--            v-model="scope.row.status"-->
<!--            active-value="A"-->
<!--            inactive-value="N"-->
<!--            @change="handleStatusChange(scope.row)"-->
<!--          ></el-switch>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-hasPermi="['goods:cate:index']"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            v-hasPermi="['goods:cate:index']"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" class="common-dialog" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="门店" prop="storeId">
              <el-select
                v-model="form.storeId"
                clearable
                :loading="storeIdLoading"
                placeholder="请选择门店"
                style="width: 100%"
              >
                <el-option
                  v-for="storeId in storeIdOptions"
                  :key="storeId.id"
                  :label="`${storeId.name}`"
                  :value="storeId.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="会员等级" prop="gradeId">
              <el-select
                v-model="form.gradeId"
                clearable
                placeholder="请选择会员等级"
                style="width: 100%"
              >
                <el-option
                  v-for="grade in gradeOptions"
                  :key="grade.id"
                  :label="`${grade.name}`"
                  :value="grade.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="最小金额（含）" prop="minAmount">
              <el-input v-model="form.minAmount" placeholder="请输入打折的最低金额"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="最大金额(不含)" prop="maxAmount">
              <el-input v-model="form.maxAmount" placeholder="请输入打折的最高金额，NULL为代表无穷大"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="返利百分比" prop="rebateRate">
              <el-input v-model="form.rebateRate" placeholder="返利的金额，例如 0.3 代表 30%"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="规则描述" prop="description">
              <el-input v-model="form.description" placeholder="请输入会员规则的描述"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="状态(默认为启用)">
              <el-radio-group v-model="form.status">
                <el-radio key="A" label="A" value="A">启用</el-radio>
                <el-radio key="N" label="N" value="N">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  deletePointRule, getAllMemberGrades, getAllMerchants, getAllStore,
  getPointRuleInfo,
  getPointRuleList,
  saveSetting, searchMerchants, upDateSetting
} from "@/api/point";

export default {
  name: "PointSetting",
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
      // 门店搜索加载状态
      storeIdLoading: false,
      // 门店选项列表
      storeIdOptions: [],
      // 会员等级搜索加载状态
      gradeLoading: false,
      // 会员等级选项列表
      gradeOptions: [],
      // 遮罩层
      loading: true,
      // 标题
      title: "",
      // 选中数组
      ids: [],
      // // 非多个禁用
      // multiple: true,
      // // 显示搜索条件
      // showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // // 升级条件
      // catchTypeList: [],
      // 是否显示弹出层
      open: false,
      // 默认排序
      defaultSort: {prop: 'createTime', order: 'descending'},
      // 表单参数
      form: {
        id: '',
        storeId: '',
        gradeId: '',
        minAmount: '',
        maxAmount: '',
        rebateRate: '',
        description: '',
        status: '',
        createTime: '',
        updateTime: ''
      },
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        name: '',
        catchType: '',
        id: '',
        merchantId: null,
        gradeId: null,
        status: null,
      },
      // 表单校验
      rules: {
        merchantId: [
          {
            required: true,
            message: "商户ID不能为空",
            trigger: "blur"
          },
          {
            validator: (rule, value, callback) => {
              if (!/^[1-9]\d*$/.test(value)) {
                callback(new Error('必须输入正整数'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        storeId: [
          {
            required: true,
            message: "门店不能为空",
            trigger: "blur"
          }
        ],
        gradeId: [
          {
            required: true,  // 保留必填规则
            message: "等级ID不能为空",
            trigger: "blur"
          },
          {
            pattern: /^(1|86|87)$/,  // 新增特定值验证
            message: '等级ID只能是1, 86, 87',
            trigger: 'blur'
          }
        ],
        minAmount: [
          {
            required: true,
            message: "最小金额不能为空",
            trigger: "blur"
          },
          {
            validator: (rule, value, callback) => {
              // 非负验证
              if (parseFloat(value) < 0) {
                callback(new Error('最小金额不能小于0'));
                return;
              }

              // 小于最大金额验证
              if (this.form.maxAmount && parseFloat(value) >= parseFloat(this.form.maxAmount)) {
                callback(new Error('最小金额必须小于最大金额'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          },
          {
            pattern: /^\d+(\.\d{1,2})?$/,  // 最多两位小数
            message: "格式不正确（数字，最多两位小数）",
            trigger: 'blur'
          }
        ],
        maxAmount: [
          {
            validator: (rule, value, callback) => {
              // 非负验证
              if (parseFloat(value) < 0) {
                callback(new Error('最大金额不能小于0'));
                return;
              }

              // 大于最小金额验证
              if (this.form.minAmount && parseFloat(value) <= parseFloat(this.form.minAmount)) {
                callback(new Error('最大金额必须大于最小金额'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          },
          {
            pattern: /^\d+(\.\d{1,2})?$/,  // 最多两位小数
            message: "格式不正确（数字，最多两位小数）",
            trigger: 'blur'
          }
        ],
        rebateRate: [
          {
            required: true,
            message: "返利率不能为空",
            trigger: "blur"
          },
          {
            validator: (rule, value, callback) => {
              // 大于0验证
              if (parseFloat(value) <= 0) {
                callback(new Error('返利率必须大于0'));
                return;
              }

              // 最多两位小数验证
              if (!/^\d+(\.\d{1,2})?$/.test(value)) {
                callback(new Error('最多保留两位小数'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        description: [
          // 非必填，不需要required规则
          {
            max: 200,
            message: "描述不能超过200个字符",
            trigger: 'blur'
          }
        ]
      }
    };
  },
  created() {
    this.getList();
    // 获取所有商户
    this.getAllMerchants();
    // 获取所有门店
    this.getAllStore();
    //获取所有会员等级
    this.getAllMemberGrades();
  },
  // activated() {
  //   this.getSettingInfo();
  // },
  methods: {
    // 格式化门店ID为门店名称

    formatStoreId(row, column) {
      const storeId = row.storeId;
      const store = this.storeIdOptions.find(s => s.id == storeId);

      if (store) {
        return store.name;
      } else {
        console.warn(`未找到匹配的门店ID: ${storeId}`);
        return `未知门店(${storeId})`;
      }
    },
    // 会员等级ID为会员等级名称
    formatGrade(row, column) {
      const gradeId = row.gradeId;
      const grade = this.gradeOptions.find(s => s.id === gradeId);
      return grade ? grade.name : `未知门店(${gradeId})`;
    },
    // 格式化规则描述，空时显示"无"
    formatDescription(row, column) {
      return row.description || "无";
    },
    // 格式化最大金额，空时显示"无穷大"
    formatMaxAmount(row, column) {
      return row.maxAmount === null || row.maxAmount === '' ? "无穷大" : row.maxAmount;
    },
    // 格式化状态显示
    formatStatus(row, column) {
      const statusMap = {
        'A': '启用',
        'N': '禁用'
      };
      return statusMap[row.status] || row.status;
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    // 重置按钮操作
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
      this.handleQuery();
    },
    // 搜索商户
    getAllMerchants() {
      this.merchantLoading = true;
      getAllMerchants().then(response => {
        if (response.data && response.data.merchantList) {
          this.merchantOptions = response.data.merchantList;
        } else {
          this.$message.error('获取商户列表失败');
        }
      }).catch(error => {
        console.error("获取商户失败:", error);
        this.$message.error('商户加载失败');
      }).finally(() => {
        this.merchantLoading = false;
      });
    },
    // 搜索门店（店铺）
    getAllStore() {
      this.storeIdLoading = true;
      getAllStore().then(response => {

        // 确保正确访问分页数据
        if (response.data && response.data.paginationResponse && response.data.paginationResponse.content) {
          // 处理门店数据 - 映射为 {id, name} 结构
          this.storeIdOptions = response.data.paginationResponse.content.map(store => ({
            id: store.id,
            name: store.name
          }));

        } else {
          this.$message.error('获取门店列表失败，数据结构异常');
          console.error('无效的门店数据结构:', response.data);
        }
      }).catch(error => {
        console.error("获取门店失败:", error);
        this.$message.error('门店加载失败');
      }).finally(() => {
        this.storeIdLoading = false;
      });
    },
    // 获取所有会员等级
    getAllMemberGrades() {
      this.gradeLoading = true;
      getAllMemberGrades().then(response => {
        if (response.data && response.data.paginationResponse && response.data.paginationResponse.content) {
          this.gradeOptions = response.data.paginationResponse.content;
        } else {
          this.$message.error('获取会员等级列表失败');
        }
      }).catch(error => {
        console.error("获取会员等级失败:", error);
        this.$message.error('会员等级加载失败');
      }).finally(() => {
        this.gradeLoading = false;
      });
    },

    // 分页查询规则列表
    getList() {
      this.loading = true;
      getPointRuleList(this.queryParams).then(response => {
        this.list = response.data.content;
        this.total = response.data.totalElements;
        this.loading = false;
      }).catch(error => {
        this.loading = false;
        this.$message.error('数据加载失败: ' + error.message);
      });
    },
    // 取消按钮
    cancel() {
      this.$store.dispatch('tagsView/delView', this.$route)
      // this.$router.push( { path: '/point/setting' } );
      this.open = false;
    },
    // 提交按钮
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id) {
            upDateSetting(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            saveSetting(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 排序触发事件
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getPointRuleInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "编辑积分规则";
      });
    },
    // 删除按钮操作
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除"' + id + '"的数据项？').then(function() {
        console.log(id)
        return deletePointRule(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 新增按钮操作
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增积分规则";
    },
    // 表单重置
    reset() {
      this.form = {
        id: "",
        merchantId: "",
        gradeId: "",
        minAmount: "",
        maxAmount: "",
        rebateRate: "",
        description: "",
        status: "",
        createTime: "",
        updateTime: ""
      };
      this.resetForm("form");
    },
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
.main-panel {
  .content {
    margin-left: 50px;
    margin-top: 30px;
  }
  .footer {
    margin-top: 10px;
    margin-left: 250px;
  }
}
</style>
